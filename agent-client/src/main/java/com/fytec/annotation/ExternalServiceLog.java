package com.fytec.annotation;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.io.InputStream;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 外部服务调用日志注解
 * 用于标记需要记录外部服务调用日志的方法
 * 支持多种文件输入类型：file、inputstream、base64、bytes、url、SseEmitter
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExternalServiceLog {
    
    /**
     * 服务类型
     */
    String serviceType() default "";
    
    /**
     * 服务描述
     */
    String description() default "";
    
    /**
     * 数据参数名称
     * 指定方法中哪个参数是数据参数
     */
    String dataParamName() default "";
    
    /**
     * 数据参数类型
     * 指定数据参数的预期类型
     */
    DataParamType dataParamType() default DataParamType.AUTO;
    
    /**
     * 支持的输入类型
     */
    InputType[] supportedTypes() default {InputType.ALL};
    
    /**
     * 输入类型枚举
     */
    enum InputType {
        FILE,           // File类型
        INPUT_STREAM,   // InputStream类型
        BASE64,         // Base64字符串
        BYTES,          // byte[]数组
        URL,            // URL字符串
        SSE_EMITTER,    // SseEmitter类型
        TEXT,           // 文本类型
        ALL             // 支持所有类型
    }
    
    /**
     * 数据参数类型枚举
     */
    enum DataParamType {
        AUTO,           // 自动识别
        FILE,           // 文件类型
        INPUT_STREAM,   // 输入流类型
        BASE64,         // Base64编码字符串
        BYTES,          // 字节数组
        URL,            // URL地址
        SSE_EMITTER,    // 服务器发送事件
        TEXT,           // 纯文本
        JSON,           // JSON格式
        XML,            // XML格式
        BINARY          // 二进制数据
    }
}