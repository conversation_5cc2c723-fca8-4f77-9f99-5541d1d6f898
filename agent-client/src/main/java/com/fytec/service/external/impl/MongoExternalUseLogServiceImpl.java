package com.fytec.service.external.impl;

import cn.hutool.core.util.StrUtil;
import com.fytec.entity.external.ExternalUseLog;
import com.fytec.mapper.external.ExternalUseLogRepo;
import com.fytec.service.external.ExternalUseLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * MongoDB外部服务使用日志服务实现类
 */
@Service
@AllArgsConstructor
@Slf4j
public class MongoExternalUseLogServiceImpl implements ExternalUseLogService {

    private final ExternalUseLogRepo externalUseLogRepo;
    private final MongoTemplate mongoTemplate;

    @Override
    public void saveExternalUseLog(ExternalUseLog externalUseLog) {
        try {
            // 设置创建和更新时间
            externalUseLog.prePersist();
            
            // 保存到MongoDB
            ExternalUseLog savedLog = externalUseLogRepo.save(externalUseLog);
            
            log.debug("外部服务使用日志保存成功 - ID: {}, 客户端: {}, 服务类型: {}, 调用次数: {}",
                savedLog.getId(), savedLog.getClientId(), savedLog.getLogType(), savedLog.getCalculateCount());
                
        } catch (Exception e) {
            log.error("保存外部服务使用日志失败 - 客户端: {}, 服务类型: {}, 错误: {}",
                externalUseLog.getClientId(), externalUseLog.getLogType(), e.getMessage(), e);
            throw new RuntimeException("保存外部服务使用日志失败", e);
        }
    }

    @Override
    public Long countByClientAndService(String clientName, String serviceType) {
        try {
            Query query = new Query();
            
            if (StrUtil.isNotBlank(clientName)) {
                query.addCriteria(Criteria.where("client_name").is(clientName));
            }
            
            if (StrUtil.isNotBlank(serviceType)) {
                query.addCriteria(Criteria.where("log_type").is(serviceType));
            }
            
            return mongoTemplate.count(query, ExternalUseLog.class);
            
        } catch (Exception e) {
            log.error("统计外部服务调用次数失败 - 客户端: {}, 服务类型: {}, 错误: {}", 
                clientName, serviceType, e.getMessage(), e);
            return 0L;
        }
    }
}
