package com.fytec.service.external;

import com.fytec.entity.external.ExternalUseLog;

/**
 * 外部服务使用日志服务接口
 */
public interface ExternalUseLogService {
    
    /**
     * 保存外部服务使用日志
     * 
     * @param externalUseLog 外部服务使用日志实体
     */
    void saveExternalUseLog(ExternalUseLog externalUseLog);
    
    /**
     * 根据客户端名称统计调用次数
     * 
     * @param clientName 客户端名称
     * @param serviceType 服务类型
     * @return 调用次数
     */
    Long countByClientAndService(String clientName, String serviceType);
}
