package com.fytec.client.external;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fytec.dto.external.TtsRequestDTO;
import com.fytec.utils.external.XfyunSignUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 讯飞语音合成WebSocket客户端（通用版）
 *
 * <AUTHOR>
 */
@Slf4j
public class XfyunTtsWebSocketClient extends WebSocketListener {

    private final String hostUrl;
    private final String appId;
    private final String apiSecret;
    private final String apiKey;
    private final TtsRequestDTO request;
    private final Consumer<JsonNode> onMessage;
    private final Consumer<Throwable> onError;
    private final Runnable onComplete;
    private final ObjectMapper objectMapper;
    private final ScheduledExecutorService executor;
    
    @Getter
    private WebSocket webSocket;
    
    @Getter
    private final CompletableFuture<Void> webSocketConnectedFuture;

    public XfyunTtsWebSocketClient(String hostUrl, String appId, String apiSecret, String apiKey,
                                   TtsRequestDTO request, Consumer<JsonNode> onMessage,
                                   Consumer<Throwable> onError, Runnable onComplete) {
        this.hostUrl = hostUrl;
        this.appId = appId;
        this.apiSecret = apiSecret;
        this.apiKey = apiKey;
        this.request = request;
        this.onMessage = onMessage;
        this.onError = onError;
        this.onComplete = onComplete;
        this.objectMapper = new ObjectMapper();
        this.executor = new ScheduledThreadPoolExecutor(2, r -> {
            Thread thread = new Thread(r, "XfyunTts-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });
        this.webSocketConnectedFuture = new CompletableFuture<>();
    }
    
    public void connect() {
        try {
            String authUrl = XfyunSignUtil.buildAuthUrl(hostUrl, appId, apiSecret, apiKey);
            String wsUrl = authUrl.replace("http://", "ws://").replace("https://", "wss://");
            
            OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
            
            Request request = new Request.Builder()
                .url(wsUrl)
                .build();
            
            this.webSocket = client.newWebSocket(request, this);
            log.info("开始连接讯飞语音合成WebSocket: {}", wsUrl);
        } catch (Exception e) {
            log.error("连接讯飞语音合成WebSocket失败", e);
            onError.accept(e);
        }
    }

    @Override
    public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
        log.info("讯飞语音合成WebSocket连接已建立");
        webSocketConnectedFuture.complete(null);
        
        // 连接建立后立即发送文本数据
        sendTextData();
    }

    @Override
    public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
        try {
            JsonNode jsonNode = objectMapper.readTree(text);
            log.debug("收到讯飞TTS响应: {}", text);
            
            // 检查错误码
            JsonNode codeNode = jsonNode.path("code");
            if (!codeNode.isMissingNode() && codeNode.asInt() != 0) {
                String errorMsg = String.format("讯飞TTS返回错误，错误码: %d, 错误信息: %s", 
                    codeNode.asInt(), jsonNode.path("message").asText("未知错误"));
                log.error(errorMsg);
                onError.accept(new RuntimeException(errorMsg));
                return;
            }
            
            // 处理正常响应
            onMessage.accept(jsonNode);
            
            // 检查是否合成完成
            JsonNode dataNode = jsonNode.path("data");
            if (!dataNode.isMissingNode() && !dataNode.isNull()) {
                JsonNode statusNode = dataNode.path("status");
                if (!statusNode.isMissingNode() && statusNode.asInt() == 2) {
                    log.info("讯飞语音合成完成");
                    onComplete.run();
                    close();
                }
            }
        } catch (Exception e) {
            log.error("处理讯飞TTS响应失败", e);
            onError.accept(e);
        }
    }

    @Override
    public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, Response response) {
        log.error("讯飞语音合成WebSocket连接失败", t);
        webSocketConnectedFuture.completeExceptionally(t);
        onError.accept(t);
    }

    @Override
    public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
        log.info("讯飞语音合成WebSocket连接已关闭，代码: {}, 原因: {}", code, reason);
        executor.shutdown();
    }

    /**
     * 发送文本数据进行语音合成
     */
    private void sendTextData() {
        executor.submit(() -> {
            try {
                // 构建请求JSON
                ObjectNode requestJson = objectMapper.createObjectNode();
                
                // common参数
                ObjectNode common = objectMapper.createObjectNode();
                common.put("app_id", appId);
                requestJson.set("common", common);
                
                // business参数
                ObjectNode business = objectMapper.createObjectNode();
                business.put("aue", request.getAue());
                if (request.getSfl() != null) {
                    business.put("sfl", request.getSfl());
                }
                if (request.getAuf() != null) {
                    business.put("auf", request.getAuf());
                }
                business.put("vcn", request.getVcn());
                business.put("speed", request.getSpeed());
                business.put("volume", request.getVolume());
                business.put("pitch", request.getPitch());
                if (request.getBgs() != null) {
                    business.put("bgs", request.getBgs());
                }
                business.put("tte", request.getTte());
                if (request.getReg() != null) {
                    business.put("reg", request.getReg());
                }
                if (request.getRdn() != null) {
                    business.put("rdn", request.getRdn());
                }
                requestJson.set("business", business);

                // data参数
                ObjectNode data = objectMapper.createObjectNode();
                data.put("status", 2); // TTS一次性发送，状态固定为2
                data.put("text", Base64.getEncoder().encodeToString(request.getText().getBytes(StandardCharsets.UTF_8)));
                requestJson.set("data", data);
                
                String requestStr = objectMapper.writeValueAsString(requestJson);
                log.debug("发送TTS请求: {}", requestStr);
                
                webSocket.send(requestStr);
            } catch (Exception e) {
                log.error("发送TTS文本数据失败", e);
                onError.accept(e);
            }
        });
    }

    /**
     * 关闭WebSocket连接
     */
    public void close() {
        if (webSocket != null) {
            webSocket.close(1000, "正常关闭");
        }
        executor.shutdown();
    }
}
