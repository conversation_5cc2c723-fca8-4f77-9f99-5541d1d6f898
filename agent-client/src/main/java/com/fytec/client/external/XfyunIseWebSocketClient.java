package com.fytec.client.external;

import cn.hutool.core.codec.Base64;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fytec.utils.external.XfyunSignUtil;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 讯飞语音评测WebSocket客户端
 *
 * <AUTHOR>
 */
@Slf4j
public class XfyunIseWebSocketClient extends WebSocketListener {

    private static final int STATUS_FIRST_FRAME = 0;
    private static final int STATUS_CONTINUE_FRAME = 1;
    private static final int STATUS_LAST_FRAME = 2;
    private static final int FRAME_SIZE = 1280;
    private static final int SEND_INTERVAL_MS = 40;
    
    private final String hostUrl;
    private final String appId;
    private final String apiSecret;
    private final String apiKey;
    private final String text;
    private final String encoding;
    private final Consumer<JsonNode> onMessage;
    private final Consumer<Throwable> onError;
    private final Runnable onComplete;
    private final ObjectMapper objectMapper;
    private final ScheduledExecutorService executor;
    
    private WebSocket webSocket;
    @Getter
    private final CompletableFuture<Void> webSocketConnectedFuture;
    
    public XfyunIseWebSocketClient(String hostUrl, String appId, String apiSecret, String apiKey,
                                   String text, String encoding, Consumer<JsonNode> onMessage,
                                   Consumer<Throwable> onError, Runnable onComplete) {
        this.hostUrl = hostUrl;
        this.appId = appId;
        this.apiSecret = apiSecret;
        this.apiKey = apiKey;
        this.text = text;
        this.encoding = encoding;
        this.onMessage = onMessage;
        this.onError = onError;
        this.onComplete = onComplete;
        this.objectMapper = new ObjectMapper();
        this.executor = new ScheduledThreadPoolExecutor(2);
        this.webSocketConnectedFuture = new CompletableFuture<>();
    }
    
    public void connect() {
        try {
            String authUrl = XfyunSignUtil.buildAuthUrl(hostUrl, appId, apiSecret, apiKey);
            String wsUrl = authUrl.replace("http://", "ws://").replace("https://", "wss://");
            
            OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();
            
            Request request = new Request.Builder()
                .url(wsUrl)
                .build();
            
            this.webSocket = client.newWebSocket(request, this);
            log.info("开始连接讯飞语音评测WebSocket: {}", wsUrl);
        } catch (Exception e) {
            log.error("连接讯飞语音评测WebSocket失败", e);
            onError.accept(e);
        }
    }
    
    @Override
    public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
        log.info("讯飞语音评测WebSocket连接已建立");
        webSocketConnectedFuture.complete(null);
    }
    
    @Override
    public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
        try {
            JsonNode response = objectMapper.readTree(text);
            
            // 检查错误码
            int code = response.path("code").asInt();
            if (code != 0) {
                String message = response.path("message").asText();
                String sid = response.path("sid").asText();
                log.error("语音评测错误，code: {}, message: {}, sid: {}", code, message, sid);
                onError.accept(new RuntimeException("语音评测错误: " + message));
                return;
            }
            JsonNode data = response.path("data");
            
            // 直接传递原始响应数据到service层处理
            onMessage.accept(data);
            
            // 检查是否完成
            if (data.path("status").asInt() == 2) {
                log.info("语音评测完成");
                webSocket.close(1000, "评测完成");
                onComplete.run();
            }
        } catch (Exception e) {
            log.error("处理语音评测响应失败", e);
            onError.accept(e);
        }
    }
    
    @Override
    public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable t, Response response) {
        log.error("讯飞语音评测WebSocket连接失败", t);
        webSocketConnectedFuture.completeExceptionally(t);
        onError.accept(t);
    }
    
    @Override
    public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
        log.info("讯飞语音评测WebSocket连接已关闭，code: {}, reason: {}", code, reason);
        executor.shutdown();
    }
    
    public void sendAudioFile(File audioFile) {
        executor.submit(() -> {
            try {
                // 发送开始帧
                sendStartFrame();
                
                // 发送音频数据
                try (FileInputStream fis = new FileInputStream(audioFile)) {
                    byte[] buffer = new byte[FRAME_SIZE];
                    int status = STATUS_FIRST_FRAME;
                    
                    while (true) {
                        int len = fis.read(buffer);
                        if (len == -1) {
                            status = STATUS_LAST_FRAME;
                        }
                        
                        switch (status) {
                            case STATUS_FIRST_FRAME:
                                sendAudioFrame(1, 1, Base64.encode(
                                        Arrays.copyOf(buffer, len)));
                                status = STATUS_CONTINUE_FRAME;
                                break;
                            case STATUS_CONTINUE_FRAME:
                                sendAudioFrame(2, 1, Base64.encode(
                                        Arrays.copyOf(buffer, len)));
                                break;
                            case STATUS_LAST_FRAME:
                                sendAudioFrame(4, 2, "");
                                return;
                        }
                        
                        Thread.sleep(SEND_INTERVAL_MS);
                    }
                }
            } catch (Exception e) {
                log.error("发送音频文件失败", e);
                onError.accept(e);
            }
        });
    }
    
    private void sendStartFrame() {
        try {
            ObjectNode frame = objectMapper.createObjectNode();
            
            ObjectNode common = objectMapper.createObjectNode();
            common.put("app_id", appId);
            
            ObjectNode business = objectMapper.createObjectNode();
            business.put("category", "read_sentence");
            business.put("rstcd", "utf8");
            business.put("sub", "ise");
            business.put("ent", "cn_vip");
            business.put("tte", "utf-8");
            business.put("cmd", "ssb");
            business.put("auf", "audio/L16;rate=16000");
            business.put("aue", encoding);
            business.put("text", "\uFEFF" + text);
            
            ObjectNode data = objectMapper.createObjectNode();
            data.put("status", 0);
            data.put("data", "");
            
            frame.set("common", common);
            frame.set("business", business);
            frame.set("data", data);
            
            String frameJson = objectMapper.writeValueAsString(frame);
            webSocket.send(frameJson);
            log.debug("发送开始帧: {}", frameJson);
        } catch (Exception e) {
            log.error("发送开始帧失败", e);
            onError.accept(e);
        }
    }
    
    private void sendAudioFrame(int aus, int status, String audioData) {
        try {
            ObjectNode frame = objectMapper.createObjectNode();
            
            ObjectNode business = objectMapper.createObjectNode();
            business.put("cmd", "auw");
            business.put("aus", aus);
            business.put("aue", encoding);
            
            ObjectNode data = objectMapper.createObjectNode();
            data.put("status", status);
            data.put("data", audioData);
            data.put("data_type", 1);
            data.put("encoding", "raw");
            
            frame.set("business", business);
            frame.set("data", data);
            
            String frameJson = objectMapper.writeValueAsString(frame);
            webSocket.send(frameJson);
            log.debug("发送音频帧，aus: {}, status: {}", aus, status);
        } catch (Exception e) {
            log.error("发送音频帧失败", e);
            onError.accept(e);
        }
    }

    public void close() {
        if (webSocket != null) {
            webSocket.close(1000, "客户端主动关闭");
        }
        executor.shutdown();
    }
}