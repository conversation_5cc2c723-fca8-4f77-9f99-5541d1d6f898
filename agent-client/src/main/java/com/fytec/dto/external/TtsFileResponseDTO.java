package com.fytec.dto.external;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 语音合成文件响应DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "语音合成文件响应")
public class TtsFileResponseDTO {

    @Schema(description = "会话ID", example = "abc123")
    private String sessionId;

    @Schema(description = "文件路径", example = "/tmp/tts_audio_1234567890.pcm")
    private String filePath;

    @Schema(description = "文件名", example = "tts_audio_1234567890.pcm")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "12345")
    private Long fileSize;

    @Schema(description = "音频格式", example = "pcm")
    private String audioFormat;

    @Schema(description = "合成状态", example = "SUCCESS")
    private String status;

    @Schema(description = "错误信息（如果有）")
    private String errorMessage;

    @Schema(description = "合成耗时（毫秒）", example = "1500")
    private Long duration;

    public static TtsFileResponseDTO success(String sessionId, String filePath, String fileName, 
                                           Long fileSize, String audioFormat, Long duration) {
        TtsFileResponseDTO response = new TtsFileResponseDTO();
        response.setSessionId(sessionId);
        response.setFilePath(filePath);
        response.setFileName(fileName);
        response.setFileSize(fileSize);
        response.setAudioFormat(audioFormat);
        response.setStatus("SUCCESS");
        response.setDuration(duration);
        return response;
    }

    public static TtsFileResponseDTO error(String sessionId, String errorMessage) {
        TtsFileResponseDTO response = new TtsFileResponseDTO();
        response.setSessionId(sessionId);
        response.setStatus("ERROR");
        response.setErrorMessage(errorMessage);
        return response;
    }
}
