package com.fytec.aspect;

import com.fytec.aspect.annotation.ExternalServiceLog;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Method;

/**
 * 外部服务调用日志切面
 * 基于@ExternalServiceLog注解的切面处理
 * 支持多种文件输入类型：file、inputstream、base64、bytes、url、SseEmitter、text
 */
@Aspect
@Component
@Slf4j
@AllArgsConstructor
public class ExternalServiceLogAspect {

    /**
     * 拦截带有@ExternalServiceLog注解的方法
     * 记录外部服务调用日志
     */
    @Around("@annotation(com.fytec.aspect.annotation.ExternalServiceLog)")
    public Object logExternalService(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ExternalServiceLog annotation = method.getAnnotation(ExternalServiceLog.class);
        
        String methodName = method.getName();
        String serviceType = annotation.serviceType();
        String description = annotation.description();
        String dataParamName = annotation.dataParamName();
        ExternalServiceLog.DataParamType dataParamType = annotation.dataParamType();
        Object[] args = joinPoint.getArgs();
        
        log.info("开始执行外部服务调用 - 方法: {}, 服务类型: {}, 描述: {}, 数据参数: {}, 参数类型: {}", 
                methodName, serviceType, description, dataParamName, dataParamType);
        
        // 分析输入参数类型
        analyzeInputParameters(args, annotation.supportedTypes(), signature.getParameterNames(), dataParamName, dataParamType);
        
        try {
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            log.info("外部服务调用成功 - 方法: {}, 耗时: {}ms", methodName, (endTime - startTime));
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("外部服务调用失败 - 方法: {}, 耗时: {}ms, 错误: {}", 
                    methodName, (endTime - startTime), e.getMessage());
        }
    }
    
    /**
     * 分析输入参数类型
     * 支持多种文件输入类型的识别和处理
     * 
     * @param args 方法参数
     * @param supportedTypes 支持的输入类型
     * @param paramNames 参数名称数组
     * @param dataParamName 指定的数据参数名称
     * @param dataParamType 指定的数据参数类型
     */
    private void analyzeInputParameters(Object[] args, ExternalServiceLog.InputType[] supportedTypes, 
                                      String[] paramNames, String dataParamName, ExternalServiceLog.DataParamType dataParamType) {
        if (args == null || args.length == 0) {
            log.debug("方法无参数");
            return;
        }
        
        // 查找指定的数据参数
        int dataParamIndex = findDataParameterIndex(paramNames, dataParamName);
        
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            String paramName = (paramNames != null && i < paramNames.length) ? paramNames[i] : "param" + i;
            
            if (arg == null) {
                log.debug("参数[{}]({}): null", i, paramName);
                continue;
            }
            
            String paramType = identifyParameterType(arg);
            boolean isDataParam = (i == dataParamIndex);
            
            log.debug("参数[{}]({}): 类型={}, 值类型={}, 是否为数据参数={}", 
                    i, paramName, paramType, arg.getClass().getSimpleName(), isDataParam);
            
            // 如果是指定的数据参数，验证类型是否匹配
            if (isDataParam && dataParamType != ExternalServiceLog.DataParamType.AUTO) {
                validateDataParameterType(arg, dataParamType, paramName);
            }
            
            // 验证参数类型是否被支持
            if (!isTypeSupported(paramType, supportedTypes)) {
                log.warn("参数[{}]({})类型[{}]不在支持的类型列表中", i, paramName, paramType);
            }
        }
    }
    
    /**
     * 识别参数类型
     * 
     * @param param 参数对象
     * @return 参数类型字符串
     */
    private String identifyParameterType(Object param) {
        if (param instanceof File) {
            return "FILE";
        } else if (param instanceof InputStream) {
            return "INPUT_STREAM";
        } else if (param instanceof byte[]) {
            return "BYTES";
        } else if (param instanceof SseEmitter) {
            return "SSE_EMITTER";
        } else if (param instanceof String str) {
            if (str.startsWith("data:") || str.matches("^[A-Za-z0-9+/]*={0,2}$")) {
                return "BASE64";
            } else if (str.startsWith("http://") || str.startsWith("https://") || str.startsWith("ftp://")) {
                return "URL";
            } else {
                return "TEXT";
            }
        } else {
            return "UNKNOWN";
        }
    }
    
    /**
     * 查找数据参数的索引位置
     * 
     * @param paramNames 参数名称数组
     * @param dataParamName 指定的数据参数名称
     * @return 数据参数索引，未找到返回-1
     */
    private int findDataParameterIndex(String[] paramNames, String dataParamName) {
        if (paramNames == null || dataParamName == null || dataParamName.trim().isEmpty()) {
            return -1;
        }
        
        for (int i = 0; i < paramNames.length; i++) {
            if (dataParamName.equals(paramNames[i])) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 验证数据参数类型是否匹配
     * 
     * @param param 参数对象
     * @param expectedType 期望的参数类型
     * @param paramName 参数名称
     */
    private void validateDataParameterType(Object param, ExternalServiceLog.DataParamType expectedType, String paramName) {
        String actualType = identifyParameterType(param);
        String expectedTypeName = expectedType.name();
        
        // 特殊处理JSON和XML类型（都是字符串的子类型）
        if ((expectedType == ExternalServiceLog.DataParamType.JSON || expectedType == ExternalServiceLog.DataParamType.XML) 
            && "TEXT".equals(actualType)) {
            log.debug("数据参数[{}]类型验证通过: 期望={}, 实际={}", paramName, expectedTypeName, actualType);
            return;
        }
        
        // 二进制数据可以是字节数组或文件
        if (expectedType == ExternalServiceLog.DataParamType.BINARY 
            && ("BYTES".equals(actualType) || "FILE".equals(actualType))) {
            log.debug("数据参数[{}]类型验证通过: 期望={}, 实际={}", paramName, expectedTypeName, actualType);
            return;
        }
        
        // 直接类型匹配
        if (expectedTypeName.equals(actualType)) {
            log.debug("数据参数[{}]类型验证通过: 期望={}, 实际={}", paramName, expectedTypeName, actualType);
        } else {
            log.warn("数据参数[{}]类型不匹配: 期望={}, 实际={}", paramName, expectedTypeName, actualType);
        }
    }
    
    /**
     * 检查参数类型是否被支持
     * 
     * @param paramType 参数类型
     * @param supportedTypes 支持的类型数组
     * @return 是否支持
     */
    private boolean isTypeSupported(String paramType, ExternalServiceLog.InputType[] supportedTypes) {
        for (ExternalServiceLog.InputType supportedType : supportedTypes) {
            if (supportedType == ExternalServiceLog.InputType.ALL) {
                return true;
            }
            if (supportedType.name().equals(paramType)) {
                return true;
            }
        }
        return false;
    }
}