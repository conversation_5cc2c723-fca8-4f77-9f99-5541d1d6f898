package com.fytec.aspect.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * AI服务计费监控注解
 * 用于标记需要进行计费监控的AI服务方法
 * 支持流式响应计费和多种计费策略
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BillingMonitor {
    
    /**
     * 服务类型编码
     */
    ServiceType serviceType();
    
    /**
     * 计费策略
     */
    BillingStrategy strategy() default BillingStrategy.AUTO;
    
    /**
     * 内容参数名（用于获取计费内容）
     */
    String contentParam() default "";
    
    /**
     * 是否异步记录
     */
    boolean async() default true;
    
    /**
     * 是否为流式响应
     */
    boolean streaming() default false;
    
    /**
     * 流式响应计费模式
     */
    StreamBillingMode streamMode() default StreamBillingMode.ON_COMPLETE;
    
    /**
     * 服务类型枚举
     */
    enum ServiceType {
        TTS_PREMIUM_SHORT("TTS_PREMIUM_SHORT", "精品语音合成-短文本"),
        TTS_PREMIUM_LONG("TTS_PREMIUM_LONG", "精品语音合成-长文本"),
        OCR_EDUCATION("OCR_EDUCATION", "教育场景OCR"),
        OCR_GENERAL("OCR_GENERAL", "通用场景OCR"),
        ASR_REALTIME("ASR_REALTIME", "实时语音识别"),
        ASR_FILE("ASR_FILE", "文件语音识别");
        
        private final String code;
        private final String description;
        
        ServiceType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDescription() { return description; }
    }
    
    /**
     * 计费策略枚举
     */
    enum BillingStrategy {
        AUTO,           // 自动检测
        BY_CHARACTERS,  // 按字符数
        BY_TOKENS,      // 按Token数
        BY_DURATION,    // 按时长
        BY_REQUESTS     // 按请求次数
    }
    
    /**
     * 流式计费模式枚举
     */
    enum StreamBillingMode {
        ON_COMPLETE,    // 流结束时计费
        INCREMENTAL,    // 增量计费（每个chunk计费）
        PERIODIC        // 定期计费
    }
    
}