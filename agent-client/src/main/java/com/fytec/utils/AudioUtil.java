package com.fytec.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.XMPDM;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.sax.BodyContentHandler;

import java.io.File;
import java.io.FileInputStream;
import java.time.Duration;
import java.util.Optional;

/**
 * 音频处理工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class AudioUtil {

    /**
     * 获取音频文件时长
     *
     * @param audioFile 音频文件
     * @return 音频时长，获取失败返回空
     */
    public static Optional<Duration> getAudioDuration(File audioFile) {
        if (!FileUtil.exist(audioFile)) {
            log.warn("音频文件不存在: {}", audioFile.getPath());
            return Optional.empty();
        }
        try {
            Metadata metadata = extractMetadata(audioFile);
            String durationStr = metadata.get(XMPDM.DURATION);
            if (StrUtil.isNotBlank(durationStr)) {
                double durationInSeconds = NumberUtil.parseDouble(durationStr, 0.0);
                if (durationInSeconds > 0) {
                    return Optional.of(Duration.ofMillis((long) (durationInSeconds * 1000)));
                }
            }
            log.warn("无法获取音频时长: {}", audioFile.getName());
            return Optional.empty();
        } catch (Exception e) {
            log.warn("获取音频时长失败: {}, 错误: {}", audioFile.getName(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 提取音频文件元数据
     *
     * @param audioFile 音频文件
     * @return 元数据对象
     * @throws Exception 解析异常
     */
    private static Metadata extractMetadata(File audioFile) throws Exception {
        AutoDetectParser parser = new AutoDetectParser();
        BodyContentHandler handler = new BodyContentHandler(-1);
        Metadata metadata = new Metadata();
        ParseContext context = new ParseContext();
        
        try (FileInputStream inputStream = new FileInputStream(audioFile)) {
            parser.parse(inputStream, handler, metadata, context);
        }
        
        return metadata;
    }
}