package com.fytec.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

/**
 * SSE工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SseUtil {

    private SseUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * 发送SSE消息
     *
     * @param emitter SSE发射器
     * @param data 消息数据
     */
    public static void sendMessage(SseEmitter emitter, Object data) {
        try {
            emitter.send(SseEmitter.event()
                .name("message")
                .data(data));
        } catch (IllegalStateException e) {
            log.warn("SSE连接已关闭，停止发送数据: {}", e.getMessage());
        } catch (IOException e) {
            log.error("发送SSE消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 完成SSE连接
     *
     * @param emitter SSE发射器
     */
    public static void complete(SseEmitter emitter) {
        try {
            emitter.complete();
        } catch (IllegalStateException e) {
            log.debug("SSE连接已关闭: {}", e.getMessage());
        }
    }

    /**
     * 以错误完成SSE连接
     *
     * @param emitter SSE发射器
     * @param error 错误信息
     */
    public static void completeWithError(SseEmitter emitter, Throwable error) {
        try {
            emitter.completeWithError(error);
        } catch (IllegalStateException e) {
            log.debug("SSE连接已错误关闭: {}", e.getMessage());
        }
    }
}